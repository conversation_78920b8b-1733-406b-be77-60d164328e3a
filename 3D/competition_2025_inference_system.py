#!/usr/bin/env python3
"""
2025年比赛完整推理系统
包含RGB-D目标检测、文字识别、结果输出等完整功能
"""

import os
import time
import json
import cv2
import numpy as np
from pathlib import Path
from ultralytics import YOLO
import socket
import threading
from typing import Dict, List, Tuple, Optional
import easyocr
import re

class Competition2025InferenceSystem:
    def __init__(self, model_path="competition_2025_models/competition_2025_yolo11n/weights/best.pt"):
        self.model_path = model_path
        self.model = None
        self.ocr_reader = None
        
        # 比赛类别映射
        self.category_mapping = {
            0: 'CA001',  # 衣架
            1: 'CA002',  # 牙刷
            2: 'CB001',  # 果冻
            3: 'CB002',  # 长方形状饼干
            4: 'CC001',  # 罐装饮料
            5: 'CC002',  # 瓶装饮料
            6: 'CD001',  # 香蕉
            7: 'CD002',  # 橙子
            8: 'Wxxx'    # 未知物品
        }
        
        # 网络配置
        self.server_host = "*************"  # 比赛服务器IP
        self.server_port = 8080
        
        # 性能统计
        self.performance_stats = {
            'model_loading_time': 0,
            'inference_times': [],
            'ocr_times': [],
            'total_detections': 0
        }
        
    def load_model(self):
        """加载YOLO模型"""
        print("正在加载RGB-D YOLO模型...")
        start_time = time.time()
        
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
            
        self.model = YOLO(self.model_path)
        
        loading_time = time.time() - start_time
        self.performance_stats['model_loading_time'] = loading_time
        
        print(f"模型加载完成，耗时: {loading_time:.3f}s")
        
        # 预热模型
        dummy_image = np.zeros((640, 640, 3), dtype=np.uint8)
        _ = self.model(dummy_image, verbose=False)
        print("模型预热完成")
        
    def load_ocr(self):
        """加载OCR模型"""
        print("正在加载OCR模型...")
        start_time = time.time()
        
        # 使用EasyOCR进行文字识别
        self.ocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=True)
        
        ocr_loading_time = time.time() - start_time
        print(f"OCR模型加载完成，耗时: {ocr_loading_time:.3f}s")
        
    def preprocess_rgbd(self, rgb_image, depth_image=None):
        """预处理RGB-D图像"""
        # RGB图像预处理
        if isinstance(rgb_image, str):
            rgb_image = cv2.imread(rgb_image)
            rgb_image = cv2.cvtColor(rgb_image, cv2.COLOR_BGR2RGB)
            
        # 深度图像预处理（如果提供）
        if depth_image is not None:
            if isinstance(depth_image, str):
                depth_image = cv2.imread(depth_image, cv2.IMREAD_UNCHANGED)
                
            # 深度图像归一化
            if depth_image.max() > 0:
                depth_normalized = depth_image.astype(np.float32) / depth_image.max()
            else:
                depth_normalized = np.zeros_like(depth_image, dtype=np.float32)
                
            # 将深度信息作为第4通道（当前使用可视化方式）
            # 注意：这里需要根据实际的RGB-D融合方式调整
            return rgb_image
        
        return rgb_image
        
    def detect_objects(self, image, conf_threshold=0.5):
        """目标检测"""
        if self.model is None:
            raise RuntimeError("模型未加载，请先调用load_model()")
            
        start_time = time.time()
        
        # 运行推理
        results = self.model(image, conf=conf_threshold, verbose=False)
        
        inference_time = time.time() - start_time
        self.performance_stats['inference_times'].append(inference_time)
        
        # 解析结果
        detections = []
        if results[0].boxes is not None:
            boxes = results[0].boxes.xyxy.cpu().numpy()
            confidences = results[0].boxes.conf.cpu().numpy()
            classes = results[0].boxes.cls.cpu().numpy().astype(int)
            
            for box, conf, cls in zip(boxes, confidences, classes):
                detection = {
                    'category_id': cls,
                    'category_name': self.category_mapping.get(cls, 'Unknown'),
                    'confidence': float(conf),
                    'bbox': box.tolist(),
                    'center': [(box[0] + box[2]) / 2, (box[1] + box[3]) / 2]
                }
                detections.append(detection)
                
        self.performance_stats['total_detections'] += len(detections)
        
        return detections, inference_time
        
    def recognize_text_in_unknown_objects(self, image, unknown_detections):
        """对未知物品进行文字识别"""
        if self.ocr_reader is None:
            print("OCR模型未加载，跳过文字识别")
            return unknown_detections
            
        start_time = time.time()
        
        for detection in unknown_detections:
            if detection['category_name'] == 'Wxxx':
                # 提取检测框区域
                bbox = detection['bbox']
                x1, y1, x2, y2 = map(int, bbox)
                roi = image[y1:y2, x1:x2]
                
                if roi.size > 0:
                    try:
                        # OCR识别
                        ocr_results = self.ocr_reader.readtext(roi)
                        
                        # 提取文字
                        texts = []
                        for (bbox_ocr, text, confidence) in ocr_results:
                            if confidence > 0.5:  # 置信度阈值
                                texts.append(text)
                                
                        if texts:
                            detection['ocr_text'] = ' '.join(texts)
                            detection['refined_category'] = self.classify_unknown_object(texts)
                        else:
                            detection['ocr_text'] = ''
                            detection['refined_category'] = 'Wxxx'
                            
                    except Exception as e:
                        print(f"OCR识别失败: {e}")
                        detection['ocr_text'] = ''
                        detection['refined_category'] = 'Wxxx'
                        
        ocr_time = time.time() - start_time
        self.performance_stats['ocr_times'].append(ocr_time)
        
        return unknown_detections
        
    def classify_unknown_object(self, texts):
        """根据OCR结果分类未知物品"""
        text_combined = ' '.join(texts).lower()
        
        # 简单的关键词匹配
        if any(keyword in text_combined for keyword in ['衣架', 'hanger']):
            return 'CA001'
        elif any(keyword in text_combined for keyword in ['牙刷', 'toothbrush']):
            return 'CA002'
        elif any(keyword in text_combined for keyword in ['果冻', 'jelly']):
            return 'CB001'
        elif any(keyword in text_combined for keyword in ['饼干', 'cookie', 'biscuit']):
            return 'CB002'
        elif any(keyword in text_combined for keyword in ['饮料', 'drink', '可乐', 'cola']):
            return 'CC001' if '罐' in text_combined else 'CC002'
        elif any(keyword in text_combined for keyword in ['香蕉', 'banana']):
            return 'CD001'
        elif any(keyword in text_combined for keyword in ['橙子', 'orange']):
            return 'CD002'
        else:
            return 'Wxxx'
            
    def count_objects(self, detections):
        """统计各类别物品数量"""
        counts = {}
        
        for detection in detections:
            category = detection.get('refined_category', detection['category_name'])
            if category not in counts:
                counts[category] = 0
            counts[category] += 1
            
        return counts
        
    def format_competition_output(self, counts):
        """格式化比赛输出格式 (ID;Num)"""
        output_lines = []
        
        # 按照比赛要求的顺序输出
        category_order = ['CA001', 'CA002', 'CB001', 'CB002', 'CC001', 'CC002', 'CD001', 'CD002', 'Wxxx']
        
        for category in category_order:
            count = counts.get(category, 0)
            output_lines.append(f"{category};{count}")
            
        return '\n'.join(output_lines)
        
    def send_results_to_server(self, results_text):
        """发送结果到比赛服务器"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(10)  # 10秒超时
                sock.connect((self.server_host, self.server_port))
                
                # 发送结果
                sock.sendall(results_text.encode('utf-8'))
                
                # 接收确认
                response = sock.recv(1024).decode('utf-8')
                print(f"服务器响应: {response}")
                
                return True
                
        except Exception as e:
            print(f"发送结果失败: {e}")
            return False
            
    def save_results_to_file(self, results_text, filename="competition_results.txt"):
        """保存结果到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(results_text)
        print(f"结果已保存到: {filename}")
        
    def process_single_image(self, rgb_image_path, depth_image_path=None, save_visualization=True):
        """处理单张图像"""
        print(f"处理图像: {rgb_image_path}")
        
        # 加载和预处理图像
        rgb_image = cv2.imread(rgb_image_path)
        if rgb_image is None:
            raise ValueError(f"无法加载图像: {rgb_image_path}")
            
        rgb_image = cv2.cvtColor(rgb_image, cv2.COLOR_BGR2RGB)
        
        # 深度图像（如果提供）
        depth_image = None
        if depth_image_path and os.path.exists(depth_image_path):
            depth_image = cv2.imread(depth_image_path, cv2.IMREAD_UNCHANGED)
            
        # 预处理
        processed_image = self.preprocess_rgbd(rgb_image, depth_image)
        
        # 目标检测
        detections, inference_time = self.detect_objects(processed_image)
        
        # 文字识别（针对未知物品）
        unknown_detections = [d for d in detections if d['category_name'] == 'Wxxx']
        if unknown_detections:
            detections = self.recognize_text_in_unknown_objects(rgb_image, detections)
            
        # 统计数量
        counts = self.count_objects(detections)
        
        # 格式化输出
        results_text = self.format_competition_output(counts)
        
        # 保存可视化结果
        if save_visualization:
            self.save_visualization(rgb_image, detections, rgb_image_path)
            
        return {
            'detections': detections,
            'counts': counts,
            'results_text': results_text,
            'inference_time': inference_time,
            'total_objects': len(detections)
        }
        
    def save_visualization(self, image, detections, original_path):
        """保存可视化结果"""
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.imshow(image)
        
        colors = plt.cm.Set3(np.linspace(0, 1, 9))
        
        for detection in detections:
            bbox = detection['bbox']
            x1, y1, x2, y2 = bbox
            width = x2 - x1
            height = y2 - y1
            
            category = detection.get('refined_category', detection['category_name'])
            confidence = detection['confidence']
            
            # 绘制边界框
            rect = patches.Rectangle(
                (x1, y1), width, height,
                linewidth=2, edgecolor=colors[detection['category_id']], facecolor='none'
            )
            ax.add_patch(rect)
            
            # 添加标签
            label = f"{category}: {confidence:.2f}"
            if 'ocr_text' in detection and detection['ocr_text']:
                label += f"\nOCR: {detection['ocr_text']}"
                
            ax.text(x1, y1-5, label,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor=colors[detection['category_id']], alpha=0.7),
                   fontsize=8, color='black')
        
        ax.set_title(f"2025年比赛检测结果 - {Path(original_path).name}")
        ax.axis('off')
        
        # 保存
        output_path = f"competition_result_{Path(original_path).stem}.jpg"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"可视化结果保存到: {output_path}")
        
    def get_performance_summary(self):
        """获取性能统计摘要"""
        avg_inference_time = np.mean(self.performance_stats['inference_times']) if self.performance_stats['inference_times'] else 0
        avg_ocr_time = np.mean(self.performance_stats['ocr_times']) if self.performance_stats['ocr_times'] else 0
        
        return {
            'model_loading_time': self.performance_stats['model_loading_time'],
            'average_inference_time': avg_inference_time,
            'average_ocr_time': avg_ocr_time,
            'total_detections': self.performance_stats['total_detections'],
            'total_images_processed': len(self.performance_stats['inference_times'])
        }

def main():
    """主函数 - 演示系统使用"""
    print("=== 2025年比赛RGB-D推理系统 ===")
    
    # 创建推理系统
    system = Competition2025InferenceSystem()
    
    # 加载模型
    system.load_model()
    
    # 加载OCR（可选）
    try:
        system.load_ocr()
    except Exception as e:
        print(f"OCR加载失败: {e}")
        print("将跳过文字识别功能")
    
    # 测试图像路径
    test_image_dir = Path("competition_2025_dataset/images/test")
    if test_image_dir.exists():
        test_images = list(test_image_dir.glob("*.jpg"))[:5]  # 测试前5张图像
        
        print(f"\n开始处理 {len(test_images)} 张测试图像...")
        
        all_results = []
        for img_path in test_images:
            try:
                result = system.process_single_image(str(img_path))
                all_results.append(result)
                
                print(f"\n图像: {img_path.name}")
                print(f"检测到 {result['total_objects']} 个物品")
                print(f"推理时间: {result['inference_time']:.3f}s")
                print("检测结果:")
                print(result['results_text'])
                print("-" * 50)
                
            except Exception as e:
                print(f"处理图像失败 {img_path}: {e}")
        
        # 性能统计
        perf_summary = system.get_performance_summary()
        print(f"\n=== 性能统计 ===")
        print(f"模型加载时间: {perf_summary['model_loading_time']:.3f}s")
        print(f"平均推理时间: {perf_summary['average_inference_time']:.3f}s")
        print(f"平均OCR时间: {perf_summary['average_ocr_time']:.3f}s")
        print(f"总检测数: {perf_summary['total_detections']}")
        
    else:
        print(f"测试图像目录不存在: {test_image_dir}")
        print("请确保已运行数据集准备脚本")

if __name__ == "__main__":
    main()
